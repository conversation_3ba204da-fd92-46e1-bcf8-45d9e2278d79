<?php
session_start();
require '../koneksi.php';

if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    $verification_id = $_POST['verification_id'];
    $status = $_POST['status'];
    $verified_by = $_SESSION['id_petugas'];
    $verified_at = date('Y-m-d H:i:s');
    
    // Update status verifikasi
    $update_verification = mysqli_query($koneksi, 
        "UPDATE verifikasi SET 
         status_verifikasi='$status', 
         verified_by='$verified_by', 
         verified_at='$verified_at' 
         WHERE id='$verification_id'"
    );
    
    if ($update_verification) {
        // Ambil tugas_id dari verifikasi
        $get_task = mysqli_query($koneksi, "SELECT tugas_id FROM verifikasi WHERE id='$verification_id'");
        $task_data = mysqli_fetch_array($get_task);
        $tugas_id = $task_data['tugas_id'];
        
        // Update status tugas proyek berdasarkan verifikasi
        if ($status == 'approved') {
            // Jika disetujui, update tugas menjadi selesai dan progress 100%
            $update_task = mysqli_query($koneksi, 
                "UPDATE tugas_proyek SET 
                 status='selesai', 
                 progress_percentage=100,
                 tgl_selesai=CURDATE(),
                 verified_by='$verified_by',
                 verified_at='$verified_at'
                 WHERE id='$tugas_id'"
            );
        } elseif ($status == 'rejected') {
            // Jika ditolak, update status menjadi batal
            $update_task = mysqli_query($koneksi, 
                "UPDATE tugas_proyek SET 
                 status='batal',
                 progress_percentage=0
                 WHERE id='$tugas_id'"
            );
        } elseif ($status == 'revision') {
            // Jika perlu revisi, kembalikan ke proses
            $update_task = mysqli_query($koneksi, 
                "UPDATE tugas_proyek SET 
                 status='proses',
                 progress_percentage=50
                 WHERE id='$tugas_id'"
            );
        } elseif ($status == 'pending') {
            // Jika reset ke pending, kembalikan ke proses
            $update_task = mysqli_query($koneksi, 
                "UPDATE tugas_proyek SET 
                 status='proses',
                 progress_percentage=75,
                 verified_by=NULL,
                 verified_at=NULL
                 WHERE id='$tugas_id'"
            );
        }
        
        // Set success message
        $_SESSION['success_message'] = 'Verifikasi berhasil diproses!';
    } else {
        $_SESSION['error_message'] = 'Gagal memproses verifikasi: ' . mysqli_error($koneksi);
    }
    
    header("Location: verifikasi.php");
    exit;
} else {
    header("Location: verifikasi.php");
    exit;
}
?>
