<?php
session_start();
require '../koneksi.php';

if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    $id = $_POST['id'];
    $status = $_POST['status'];
    $updated_by = $_SESSION['id_petugas'];

    // Update progress percentage berdasarkan status
    $progress_percentage = 0;
    switch($status) {
        case 'pending':
            $progress_percentage = 0;
            break;
        case 'proses':
            $progress_percentage = 50;
            break;
        case 'selesai':
            $progress_percentage = 100;
            break;
        case 'batal':
            $progress_percentage = 0;
            break;
        case 'verifikasi':
            $progress_percentage = 90;
            break;
    }

    // Update tugas proyek
    $update = mysqli_query($koneksi,
        "UPDATE tugas_proyek SET
         status='$status',
         progress_percentage='$progress_percentage',
         updated_at=NOW()
         WHERE id='$id'"
    );

    if ($update) {
        // Jika status diubah ke 'verifikasi', buat entry verifikasi baru
        if ($status == 'verifikasi') {
            // Cek apakah sudah ada entry verifikasi untuk tugas ini
            $check_verification = mysqli_query($koneksi, "SELECT id FROM verifikasi WHERE tugas_id='$id'");

            if (mysqli_num_rows($check_verification) == 0) {
                // Buat entry verifikasi baru
                $insert_verification = mysqli_query($koneksi,
                    "INSERT INTO verifikasi (tugas_id, status_verifikasi, created_at)
                     VALUES ('$id', 'pending', NOW())"
                );
            } else {
                // Update existing verification to pending
                mysqli_query($koneksi,
                    "UPDATE verifikasi SET
                     status_verifikasi='pending',
                     verified_by=NULL,
                     verified_at=NULL
                     WHERE tugas_id='$id'"
                );
            }
        }

        // Set tanggal mulai jika status berubah ke proses
        if ($status == 'proses') {
            mysqli_query($koneksi,
                "UPDATE tugas_proyek SET tgl_mulai=CURDATE() WHERE id='$id' AND tgl_mulai IS NULL"
            );
        }

        // Set tanggal selesai jika status berubah ke selesai
        if ($status == 'selesai') {
            mysqli_query($koneksi,
                "UPDATE tugas_proyek SET tgl_selesai=CURDATE() WHERE id='$id'"
            );
        }

        $_SESSION['success_message'] = 'Status tugas berhasil diupdate!';
        header("Location: tugas_harian.php");
        exit;
    } else {
        $_SESSION['error_message'] = 'Gagal update status: ' . mysqli_error($koneksi);
        header("Location: tugas_harian.php");
        exit;
    }
}
?>
